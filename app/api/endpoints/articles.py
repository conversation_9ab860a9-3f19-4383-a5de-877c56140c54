from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from app import crud, models, schemas
from app.api import deps
from app.core.pagination import (
    CursorPaginationParams,
    CursorPaginationResponse,
)
from app.core.permission_system import Action, Permission, PermissionChecker, ResourceType, Scope
from app.schemas.simple_unified_response import (
    ContentResponseOptions,
)
from app.schemas.unified_response import (
    UnifiedContentResponse,
)
from app.services.article_permission_service import article_permission_service
from app.services.content_service import article_service
from app.services.logger import get_logger
from app.services.unified_response_service import UnifiedResponseService

logger = get_logger(__name__)
router = APIRouter()


@router.post("/")
async def create_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> dict:
    """
    创建文章 - 统一响应格式
    """

    # 检查创建权限
    await PermissionChecker.require_permission(
        db,
        current_user,
        Permission(resource=ResourceType.ARTICLE, action=Action.CREATE, scope=Scope.OWN),
    )

    # 创建文章逻辑保持不变
    article = await crud.article.create(
        db=db,
        obj_in={
            "author_id": current_user.id,
            "title": "未命名文章",
            "content": "",
        },
    )

    return {"article_id": article.id}


@router.get(
    "/",
    response_model=CursorPaginationResponse[UnifiedContentResponse],
    summary="获取文章列表（通用优化版）",
)
async def get_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("id", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    category_id: int | None = Query(None, description="分类ID筛选"),
    author_id: int | None = Query(None, description="作者ID筛选"),
    status: str | None = Query(
        "published", description="状态筛选: draft, published, all, likes, favorites, history"
    ),
    include_stats: bool = Query(False, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_total: bool = Query(False, description="是否包含总数量（会增加查询成本）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> CursorPaginationResponse[UnifiedContentResponse]:
    """
    获取文章列表 - 统一接口（优化版）

    通过查询参数实现灵活的筛选和数据控制，并解决了N+1查询问题。
    """

    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )

    # 如果是获取历史记录，则必须是登录用户
    if status == "history" and not current_user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="查看历史记录需要登录")

    filters = {"category_id": category_id, "author_id": author_id, "status": status}

    # 权限判断
    is_own_articles = current_user and current_user.id == author_id
    effective_include_review = include_review and is_own_articles

    paginated_result = await crud.article.get_paginated_articles(
        db=db,
        params=pagination_params,
        filters=filters,
        current_user=current_user,
        include_review=effective_include_review,
        include_total=include_total,
    )

    options = ContentResponseOptions(
        include_content=False,
        include_stats=include_stats,
        include_review=effective_include_review,
        current_user_id=current_user.id if current_user else None,
    )

    items = [
        await UnifiedResponseService.build_content_response(db, article, options)
        for article in paginated_result.items
    ]

    response_data = CursorPaginationResponse(
        items=items,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )

    return response_data


@router.get(
    "/{article_id}", response_model=UnifiedContentResponse, summary="获取文章详情（优化版）"
)
async def get_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int = Path(..., description="文章ID"),
    include_stats: bool = Query(False, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(False, description="是否包含元数据"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> UnifiedContentResponse:
    """
    获取文章详情 - 统一接口（优化版）

    通过查询参数控制返回的数据内容，并解决了N+1查询问题。
    """
    article = await crud.article.get(
        db=db,
        id=article_id,
        options=[
            joinedload(models.Article.author),
            joinedload(models.Article.tags),
            joinedload(models.Article.category),
            joinedload(models.Article.reviews).joinedload(models.Review.reviewer),
        ],
    )
    if not article:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="文章不存在")

    # 权限检查
    can_access = await article_permission_service.check_article_access(
        db=db, content=article, current_user=current_user
    )
    if not can_access:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="没有权限访问此文章")

    # 用户行为追踪已由 BehaviorTrackingMiddleware 自动处理

    # 检查审核信息权限
    if include_review:
        can_view_review = current_user and (
            current_user.id == article.author_id or current_user.is_superuser
        )
        if not can_view_review:
            include_review = False

    # 构建响应选项
    options = ContentResponseOptions(
        include_content=True,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
        current_user_id=current_user.id if current_user else None,
    )

    # 构建响应
    response_data = await UnifiedResponseService.build_content_response(db, article, options)

    return response_data


@router.put("/{article_id}", response_model=schemas.ArticleDetail)
async def update_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    article_in: schemas.ArticleUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """更新文章
    - 如果文章从未发布变为发布，需要创建审核记录
    - 如果已发布文章的内容有更新，需要重新审核
    - 未发布的文章（草稿）可以自由修改，不需要审核
    """
    # 获取文章
    article = await crud.article.get(
        db=db, id=article_id, options=[joinedload(models.Article.tags)]
    )
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )

    # 检查更新权限
    can_update = await article_permission_service.check_article_update_permission(
        db=db, content=article, current_user=current_user
    )
    if not can_update:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新此文章",
        )

    article_in_dict = article_in.dict(exclude_unset=True)

    # 处理发布状态变更
    if "is_published" in article_in_dict:
        article = await article_service.handle_publish_status(
            db=db, content=article, is_published=article_in.is_published
        )

    # 处理内容更新
    has_content_update = bool(article_in.title or article_in.content)
    article = await article_service.handle_content_update(
        db=db, content=article, has_content_update=has_content_update
    )

    # 处理标签更新
    if article_in.tags is not None:
        article = await article_service.handle_tags_update(
            db=db, content=article, tags=article_in.tags
        )
        # 标签已由服务处理，从字典中移除以避免在基础CRUD中重复处理
        del article_in_dict["tags"]

    # 更新文章
    article = await crud.article.update(db=db, db_obj=article, obj_in=article_in_dict)
    return article


@router.delete("/{article_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_article(
    *,
    db: AsyncSession = Depends(deps.get_db),
    article_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """删除文章"""
    article = await crud.article.get(db, id=article_id)
    if not article:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文章不存在",
        )

    # 检查删除权限
    can_delete = await article_permission_service.check_article_delete_permission(
        db=db, content=article, current_user=current_user
    )
    if not can_delete:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除此文章",
        )

    # 删除关联的审核记录
    review = await crud.review.get_by_content(db, content_type="article", content_id=article.id)
    if review:
        await crud.review.remove(db=db, id=review.id)
    await crud.article.remove(db=db, id=article_id)


@router.get(
    "/users/{user_id}/articles",
    response_model=CursorPaginationResponse[UnifiedContentResponse],
    summary="获取指定用户的文章列表（优化版）",
)
async def get_user_articles(
    user_id: int,
    *,
    db: AsyncSession = Depends(deps.get_db),
    cursor: str | None = Query(None, description="游标位置，用于获取下一页数据"),
    size: int = Query(20, ge=1, le=100, description="每页大小，范围1-100"),
    order_by: str = Query("id", description="排序字段"),
    order_direction: str = Query("desc", pattern="^(asc|desc)$", description="排序方向：asc或desc"),
    status_filter: schemas.ArticleStatus = Query(
        schemas.ArticleStatus.PUBLISHED_APPROVED,
        alias="status",
        description="文章状态筛选（仅作者本人可用）",
    ),
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息（仅作者本人可用）"),
    include_total: bool = Query(False, description="是否包含总数量（会增加查询成本）"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """
    获取指定用户的文章列表（统一接口）

    **权限控制**：
    - **作者本人**：可以访问所有状态的文章，可以使用`status`参数筛选，可以获取审核信息。
    - **其他用户**：只能访问已发布且已审核通过的文章，`status`参数被忽略，不能获取审核信息。

    **支持的状态筛选**（仅作者本人）：
    - `all`: 所有文章
    - `draft`: 草稿（未发布）
    - `published_approved`: 已发布且已审核通过
    - `published_pending`: 已发布但待审核
    - `published_rejected`: 已发布但审核被拒绝
    """
    # 1. 检查目标用户是否存在
    target_user = await crud.user.get(db=db, id=user_id)
    if not target_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")

    # 2. 权限判断
    is_own_articles = current_user and current_user.id == user_id
    effective_include_review = include_review and is_own_articles

    # 3. 构建分页和查询参数
    pagination_params = CursorPaginationParams(
        cursor=cursor, size=size, order_by=order_by, order_direction=order_direction
    )

    # 4. 调用重构后的CRUD方法获取分页结果
    paginated_result = await crud.article.get_paginated_articles_by_user(
        db=db,
        user_id=user_id,
        params=pagination_params,
        status_filter=status_filter,
        is_own_articles=is_own_articles,
        include_review=effective_include_review,
        include_total=include_total,
    )

    # 5. 构建响应选项
    # 注意：这里的 include_review 传递给 build_content_response
    # 是为了利用预加载的数据，而不是再次查询
    options = ContentResponseOptions(
        include_content=False,
        include_stats=include_stats,
        include_review=effective_include_review,
        current_user_id=current_user.id if current_user else None,
    )

    # 6. 构建响应项列表 (无N+1查询)
    items = [
        await UnifiedResponseService.build_content_response(db, article, options)
        for article in paginated_result.items
    ]

    # 7. 构建并返回最终的分页响应
    return CursorPaginationResponse(
        items=items,
        has_next=paginated_result.has_next,
        has_previous=paginated_result.has_previous,
        next_cursor=paginated_result.next_cursor,
        previous_cursor=paginated_result.previous_cursor,
        total_count=paginated_result.total_count,
    )


@router.post(
    "/multiple",
    response_model=list[UnifiedContentResponse],
    summary="批量获取文章详情（优化版）",
    description="支持批量获取文章详情，优化了查询效率。",
)
async def get_multiple_articles(
    *,
    db: AsyncSession = Depends(deps.get_db),
    data: schemas.MultipleData,
    include_stats: bool = Query(True, description="是否包含统计信息"),
    include_review: bool = Query(False, description="是否包含审核信息"),
    include_meta: bool = Query(False, description="是否包含元数据"),
    include_content: bool = Query(False, description="是否包含正文内容"),
    include_category: bool = Query(True, description="是否包含分类信息"),
    include_tags: bool = Query(True, description="是否包含标签信息"),
    preserve_order: bool = Query(True, description="是否保持传入ID的顺序"),
    current_user: models.User | None = Depends(deps.get_current_user_optional),
) -> Any:
    """批量获取文章详情（优化版）

    **功能特点**：
    - 用于在其他内容中嵌入文章信息，如评论、回复等
    - 根据参数控制返回的数据内容，避免不必要的数据传输
    - 自动过滤用户无权限访问的文章
    - 支持批量预加载，避免N+1查询问题
    - 支持保持传入ID的顺序

    **权限控制**：
    - 自动过滤用户无权限访问的文章
    - 对于未登录用户，只返回已发布且已审核的公开文章
    - 对于登录用户，可以访问自己的文章和公开文章

    **性能优化**：
    - 使用selectinload预加载关联数据
    - 批量权限检查
    - 可选的数据字段，减少不必要的数据传输
    """
    # 1. 参数验证
    print("data.articleIds:", data.articleIds)
    if not data.articleIds:
        return []

    if len(data.articleIds) > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="一次最多只能获取100篇文章"
        )

    # 2. 去重并保持顺序
    unique_ids = list(dict.fromkeys(data.articleIds))  # 去重但保持顺序

    # 3. 过滤用户无权限访问的文章
    accessible_articles = await article_permission_service.filter_accessible_articles(
        db=db, user=current_user, article_ids=unique_ids
    )
    print("accessible_articles:", accessible_articles)

    if not accessible_articles:
        return []

    # 4. 批量获取文章详情（已包含预加载）
    articles = await crud.article.get_multi_by_ids(db, ids=accessible_articles)

    # 5. 构建响应选项
    options = ContentResponseOptions(
        include_content=include_content,
        include_stats=include_stats,
        include_review=include_review,
        include_meta=include_meta,
        include_category=include_category,
        include_tags=include_tags,
        current_user_id=current_user.id if current_user else None,
    )

    # 6. 构建响应项列表 (无N+1查询)
    items = [
        await UnifiedResponseService.build_content_response(db, article, options)
        for article in articles
    ]

    # 7. 如果需要保持顺序，按照原始ID顺序重新排序
    if preserve_order and len(items) > 1:
        items_dict = {item.id: item for item in items}
        ordered_items = [items_dict[id] for id in accessible_articles if id in items_dict]
        return ordered_items

    return items
