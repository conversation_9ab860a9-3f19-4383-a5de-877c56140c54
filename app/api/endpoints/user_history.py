from typing import Any

from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app import crud, models
from app.api import deps
from app.services.history_cache_service import history_service

router = APIRouter()


@router.get("/stats", response_model=dict)
async def get_user_activity_stats(
    *,
    db: AsyncSession = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户活动统计"""
    # 获取点赞统计
    total_likes = await crud.like.get_user_like_count(db, user_id=current_user.id)
    article_likes = await crud.like.get_user_like_count(
        db, user_id=current_user.id, content_type="article"
    )
    video_likes = await crud.like.get_user_like_count(
        db, user_id=current_user.id, content_type="video"
    )

    # 获取收藏统计
    total_favorites = await crud.favorite.get_user_favorite_count(db, user_id=current_user.id)
    article_favorites = await crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type="article"
    )
    video_favorites = await crud.favorite.get_user_favorite_count(
        db, user_id=current_user.id, content_type="video"
    )

    return {
        "likes": {
            "total": total_likes,
            "articles": article_likes,
            "videos": video_likes,
        },
        "favorites": {
            "total": total_favorites,
            "articles": article_favorites,
            "videos": video_favorites,
        },
    }


@router.get("/recent-activity", response_model=dict)
async def get_user_recent_activity(
    *,
    db: AsyncSession = Depends(deps.get_db),
    limit: int = Query(10, ge=1, le=50, description="返回的最大记录数"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """获取用户最近活动"""
    # 获取最近的点赞
    recent_likes = await crud.like.get_user_likes(db, user_id=current_user.id, skip=0, limit=limit)

    # 获取最近的收藏
    recent_favorites = await crud.favorite.get_user_favorites(
        db, user_id=current_user.id, skip=0, limit=limit
    )

    return {
        "recent_likes": [
            {
                "id": like.id,
                "content_type": like.content_type,
                "content_id": like.content_id,
                "created_at": like.created_at,
            }
            for like in recent_likes
        ],
        "recent_favorites": [
            {
                "id": favorite.id,
                "content_type": favorite.content_type,
                "content_id": favorite.content_id,
                "note": favorite.note,
                "created_at": favorite.created_at,
            }
            for favorite in recent_favorites
        ],
    }


@router.delete("/history", status_code=status.HTTP_204_NO_CONTENT)
async def clear_user_history(
    *,
    db: AsyncSession = Depends(deps.get_db),
    content_type: str | None = Query(None, description="内容类型过滤：article, video"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """清空用户历史浏览数据"""
    # 清空数据库中的历史记录
    await crud.history.remove_by_user(db, user_id=current_user.id, content_type=content_type)

    # 清空缓存
    if content_type:
        await history_service.clear_user_content_type_cache(current_user.id, content_type)
    else:
        await history_service.clear_user_cache(current_user.id)
