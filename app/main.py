"""应用主入口"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles

from app.api.api import api_router

# 微信公众号消息推送api
from app.api.endpoints.wechat import router as wechat_router
from app.api.middleware import ExceptionHandlerMiddleware, ResponseFormatterMiddleware
from app.api.visit_stats_middleware import VisitStatsMiddleware
from app.api.behavior_tracking_middleware import BehaviorTrackingMiddleware
from app.core.config import settings
from app.core.init_data import init_data
from app.core.pagination import add_pagination
from app.db.redis import get_redis
from app.services.logger import logger


async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 初始化Redis连接
    app.state.redis = await get_redis()

    # 初始化应用数据
    logger.info("正在初始化应用数据...")
    await init_data()
    logger.info("应用数据初始化完成")

    yield


# 创建FastAPI应用实例
app = FastAPI(
    title="Steam数据聚合API",
    description="提供Steam数据的聚合和分析服务",
    version="0.1.0",
    lifespan=lifespan,
    # 生产默认关闭默认文档
    # openapi_url=f"{settings.API_V1_STR}/openapi.json",
)

# 初始化分页功能
add_pagination(app)

# 初始化日志
logger.info("应用程序启动中...")

# 添加异常处理中间件（最先执行，确保捕获所有异常）
app.add_middleware(ExceptionHandlerMiddleware)

# 添加用户行为追踪中间件
app.add_middleware(BehaviorTrackingMiddleware)

# 添加访问统计中间件
app.add_middleware(VisitStatsMiddleware)

# 添加响应格式化中间件
app.add_middleware(ResponseFormatterMiddleware)

# 配置CORS（最后添加，最先执行）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 422错误处理器
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误 (422)"""
    errors = []
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        error_msg = error["msg"]
        error_type = error["type"]

        # 自定义错误消息
        if error_type == "missing":
            custom_msg = f"缺少必需字段: {field_path}"
        elif error_type == "type_error":
            custom_msg = f"字段类型错误: {field_path} - {error_msg}"
        elif error_type == "value_error":
            custom_msg = f"字段值错误: {field_path} - {error_msg}"
        else:
            custom_msg = f"{field_path}: {error_msg}"

        errors.append(
            {
                "field": field_path,
                "message": custom_msg,
                "type": error_type,
                "input": error.get("input"),
            }
        )

    return JSONResponse(
        status_code=422,
        content={
            "status": "error",
            "message": "请求数据验证失败",
            "errors": errors,
            "detail": "请检查请求参数格式和类型",
        },
    )


# HTTP异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": "error",
            "message": exc.detail,
            "status_code": exc.status_code,
        },
    )


# 通用异常处理器
@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """处理未捕获的异常"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "服务器内部错误",
            "detail": "请联系管理员或稍后重试",
        },
    )


@app.get("/")
async def root():
    """API根路径，返回欢迎信息"""
    return {"message": "欢迎使用Steam游戏数据聚合API"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy"}


# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)
app.include_router(wechat_router, prefix="/wechat")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
