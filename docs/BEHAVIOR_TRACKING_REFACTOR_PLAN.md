# 用户行为追踪系统重构计划

## 🎯 **重构目标**

解决当前系统中严重的功能重叠问题，建立统一、高效、可维护的用户行为追踪架构。

## 🔍 **当前问题分析**

### **1. 四重功能重叠**
```
用户浏览一个视频/文章
         │
         ▼
┌─────────────────────────────────────────────────────────────┐
│                    四重记录重叠                              │
├─────────────────┬─────────────────┬─────────────────┬───────┤
│   videos.py     │ user_behavior.py│behavior_tracking│history│
│   articles.py   │                 │    _service     │service│
│                 │                 │                 │       │
│ record_user_    │ /browse         │track_content_   │record_│
│ history()       │ /interaction    │view()           │user_  │
│                 │                 │                 │history│
│ ✅ 自动调用     │ ❌ 手动调用     │ ❌ 未使用       │ ✅ 被调│
│ ✅ 简单记录     │ ❌ 重复记录     │ ❌ 功能重复     │ ✅ 核心│
└─────────────────┴─────────────────┴─────────────────┴───────┘
```

### **2. 数据表混乱**
- `history` - 简单历史记录
- `user_browse_history` - 详细浏览记录  
- `user_interactions` - 交互行为记录

### **3. 缓存系统重叠**
- `history_service` - 历史缓存
- `recommendation_cache_service` - 推荐缓存
- `visit_cache_service` - 访问次数缓存

## 🚀 **中间件解决方案**

### **1. 核心架构**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   所有API调用   │───▶│  行为追踪中间件  │───▶│  自动记录行为   │
│                 │    │                 │    │                 │
│ - GET /articles │    │ - 自动识别行为   │    │ - 浏览历史      │
│ - GET /videos   │    │ - 统一记录格式   │    │ - 交互记录      │
│ - POST /likes   │    │ - 智能去重       │    │ - 用户画像      │
│ - POST /favorites│   │ - 性能优化       │    │ - 推荐缓存      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **2. 中间件优势**
- ✅ **自动化** - 无需手动调用
- ✅ **一致性** - 统一的记录逻辑
- ✅ **性能** - 异步处理，不阻塞响应
- ✅ **维护性** - 集中管理所有行为追踪

## 📋 **重构步骤**

### **阶段1：中间件实现** ✅
- [x] 创建 `BehaviorTrackingMiddleware`
- [x] 实现行为识别规则
- [x] 集成到应用中

### **阶段2：移除重复代码**
- [ ] 移除 `articles.py` 中的 `record_user_history` 调用
- [ ] 移除 `videos.py` 中的 `record_user_history` 调用
- [ ] 废弃 `user_behavior.py` 中的手动接口
- [ ] 统一使用 `behavior_tracking_service`

### **阶段3：数据表整合**
- [ ] 迁移 `history` 表数据到 `user_browse_history`
- [ ] 统一使用 `user_browse_history` 和 `user_interactions`
- [ ] 更新相关的 CRUD 操作

### **阶段4：缓存系统优化**
- [ ] 整合重复的缓存逻辑
- [ ] 优化缓存更新策略
- [ ] 实现批量缓存操作

### **阶段5：测试和验证**
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能测试
- [ ] 数据一致性验证

## 🛠️ **具体实施方案**

### **1. 移除重复的历史记录调用**

#### **articles.py 修改**
```python
# 移除这段代码：
try:
    if current_user:
        await record_user_history(
            db=db, user_id=current_user.id, content_type="article", content_id=article_id
        )
except Exception as e:
    logger.error(f"记录历史失败: {e}")
```

#### **videos.py 修改**
```python
# 移除这段代码：
try:
    if current_user:
        await record_user_history(
            db=db, user_id=current_user.id, content_type="video", content_id=video_id
        )
except Exception as e:
    logger.error(f"记录视频历史失败: {e}")
```

### **2. 废弃 user_behavior.py 接口**

#### **移除的接口**
- `POST /user/behavior/browse` - 手动记录浏览
- `POST /user/behavior/interaction` - 手动记录交互

#### **保留的接口**
- `GET /user/behavior/browse-history` - 查询浏览历史
- `GET /user/behavior/interactions` - 查询交互记录
- `GET /user/behavior/interest-analysis` - 兴趣分析
- `GET /user/behavior/profile` - 用户画像

### **3. 中间件行为识别规则**

```python
BEHAVIOR_PATTERNS = {
    # 内容浏览 (自动记录)
    "content_view": [
        r"/api/v1/articles/(\d+)$",
        r"/api/v1/videos/(\d+)$",
    ],
    
    # 交互行为 (自动记录)
    "like": [r"/api/v1/likes/toggle$"],
    "favorite": [r"/api/v1/favorites/toggle$"],
    "comment": [r"/api/v1/comments$"],
    
    # 搜索行为 (自动记录)
    "search": [
        r"/api/v1/articles.*[?&]search=",
        r"/api/v1/videos.*[?&]search=",
    ],
}
```

## 📊 **预期效果**

### **1. 代码简化**
- 移除 ~200 行重复代码
- 统一行为追踪逻辑
- 提高代码可维护性

### **2. 性能提升**
- 减少重复的数据库操作
- 优化缓存更新策略
- 异步处理，不阻塞响应

### **3. 数据一致性**
- 统一的数据记录格式
- 避免数据不一致问题
- 简化数据查询逻辑

### **4. 开发体验**
- 开发者无需关心行为追踪
- 自动化的数据收集
- 集中化的配置管理

## ⚠️ **注意事项**

### **1. 向后兼容**
- 保留查询接口，确保前端正常工作
- 数据迁移过程中保证服务可用
- 逐步废弃旧接口

### **2. 性能考虑**
- 中间件异步处理，避免阻塞
- 批量处理数据库操作
- 合理的缓存策略

### **3. 错误处理**
- 行为追踪失败不影响主要功能
- 完善的日志记录
- 优雅的降级策略

## 🎯 **下一步行动**

1. **立即执行**：开始移除重复的历史记录调用
2. **本周完成**：废弃 user_behavior.py 的手动接口
3. **下周完成**：数据表整合和缓存优化
4. **测试验证**：确保重构后系统稳定性

---

**重构负责人**：开发团队  
**预计完成时间**：2周  
**风险等级**：中等（有完整的回滚方案）
