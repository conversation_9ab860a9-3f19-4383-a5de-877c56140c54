# Steam 游戏数据聚合后端

这是一个基于FastAPI开发的Steam游戏数据聚合后端服务。

## 功能特点

- 使用FastAPI构建高性能API
- SQLAlchemy进行数据库操作
- Pydantic进行数据验证
- 支持环境变量配置

## 安装与运行

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行服务

```bash
uvicorn app.main:app --reload
```

## API文档

启动服务后，访问 http://localhost:8000/docs 查看API文档。

## 项目结构

```
.
├── app/                  # 应用主目录
│   ├── api/              # API路由
│   ├── core/             # 核心配置
│   ├── db/               # 数据库模型和会话
│   ├── models/           # 数据库模型
│   ├── schemas/          # Pydantic模型
│   ├── services/         # 业务逻辑
│   └── main.py           # 应用入口
├── tests/                # 测试目录
├── .env                  # 环境变量
└── pyproject.toml        # 项目配置
```