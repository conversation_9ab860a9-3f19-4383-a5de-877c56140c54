#!/usr/bin/env python3
"""
用户行为追踪中间件测试脚本

用于验证重构后的行为追踪系统是否正常工作
"""

import asyncio
import json
from datetime import datetime

import httpx


class BehaviorTrackingTester:
    """行为追踪测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(base_url=base_url)
        self.auth_token = None
    
    async def login(self, username: str = "<EMAIL>", password: str = "testpass"):
        """登录获取认证token"""
        try:
            response = await self.client.post(
                "/api/v1/auth/login",
                json={"username": username, "password": password}
            )
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                self.client.headers.update({"Authorization": f"Bearer {self.auth_token}"})
                print(f"✅ 登录成功: {username}")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    async def test_article_view(self, article_id: int = 1):
        """测试文章浏览行为追踪"""
        print(f"\n🔍 测试文章浏览追踪 (ID: {article_id})")
        
        try:
            response = await self.client.get(f"/api/v1/articles/{article_id}")
            if response.status_code == 200:
                print(f"✅ 文章访问成功: {response.status_code}")
                print("   中间件应该自动记录浏览行为")
                return True
            else:
                print(f"❌ 文章访问失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 文章访问异常: {e}")
            return False
    
    async def test_video_view(self, video_id: int = 1):
        """测试视频浏览行为追踪"""
        print(f"\n🔍 测试视频浏览追踪 (ID: {video_id})")
        
        try:
            response = await self.client.get(f"/api/v1/videos/{video_id}")
            if response.status_code == 200:
                print(f"✅ 视频访问成功: {response.status_code}")
                print("   中间件应该自动记录浏览行为")
                return True
            else:
                print(f"❌ 视频访问失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 视频访问异常: {e}")
            return False
    
    async def test_like_interaction(self, content_type: str = "article", content_id: int = 1):
        """测试点赞交互行为追踪"""
        print(f"\n❤️ 测试点赞交互追踪 ({content_type} ID: {content_id})")
        
        try:
            response = await self.client.post(
                "/api/v1/likes/toggle",
                json={"content_type": content_type, "content_id": content_id}
            )
            if response.status_code == 200:
                print(f"✅ 点赞操作成功: {response.status_code}")
                print("   中间件应该自动记录交互行为")
                return True
            else:
                print(f"❌ 点赞操作失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 点赞操作异常: {e}")
            return False
    
    async def test_favorite_interaction(self, content_type: str = "article", content_id: int = 1):
        """测试收藏交互行为追踪"""
        print(f"\n⭐ 测试收藏交互追踪 ({content_type} ID: {content_id})")
        
        try:
            response = await self.client.post(
                "/api/v1/favorites/toggle",
                json={"content_type": content_type, "content_id": content_id}
            )
            if response.status_code == 200:
                print(f"✅ 收藏操作成功: {response.status_code}")
                print("   中间件应该自动记录交互行为")
                return True
            else:
                print(f"❌ 收藏操作失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 收藏操作异常: {e}")
            return False
    
    async def test_search_behavior(self, search_query: str = "测试"):
        """测试搜索行为追踪"""
        print(f"\n🔍 测试搜索行为追踪 (查询: {search_query})")
        
        try:
            response = await self.client.get(f"/api/v1/articles?search={search_query}")
            if response.status_code == 200:
                print(f"✅ 搜索操作成功: {response.status_code}")
                print("   中间件应该自动记录搜索行为")
                return True
            else:
                print(f"❌ 搜索操作失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 搜索操作异常: {e}")
            return False
    
    async def check_behavior_records(self):
        """检查行为记录是否正确生成"""
        print(f"\n📊 检查行为记录")
        
        try:
            # 检查浏览历史
            response = await self.client.get("/api/v1/user/behavior/browse-history")
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and "data" in data:
                    items = data["data"].get("items", [])
                    print(f"✅ 浏览历史记录: {len(items)} 条")
                    if items:
                        latest = items[0]
                        print(f"   最新记录: {latest.get('content_type')} ID:{latest.get('content_id')}")
                else:
                    print("⚠️ 浏览历史响应格式异常")
            else:
                print(f"❌ 获取浏览历史失败: {response.status_code}")
            
            # 检查交互记录
            response = await self.client.get("/api/v1/user/behavior/interactions")
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, dict) and "data" in data:
                    items = data["data"].get("items", [])
                    print(f"✅ 交互记录: {len(items)} 条")
                    if items:
                        latest = items[0]
                        print(f"   最新记录: {latest.get('interaction_type')} - {latest.get('content_type')} ID:{latest.get('content_id')}")
                else:
                    print("⚠️ 交互记录响应格式异常")
            else:
                print(f"❌ 获取交互记录失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 检查行为记录异常: {e}")
    
    async def test_deprecated_apis(self):
        """测试废弃的API接口"""
        print(f"\n⚠️ 测试废弃的API接口")
        
        try:
            # 测试废弃的浏览记录接口
            response = await self.client.post(
                "/api/v1/user/behavior/browse",
                json={
                    "content_type": "article",
                    "content_id": 1,
                    "duration": 60
                }
            )
            print(f"📝 废弃的浏览记录接口: {response.status_code}")
            
            # 测试废弃的交互记录接口
            response = await self.client.post(
                "/api/v1/user/behavior/interaction",
                json={
                    "content_type": "article",
                    "content_id": 1,
                    "interaction_type": "view",
                    "weight": 1.0
                }
            )
            print(f"📝 废弃的交互记录接口: {response.status_code}")
            
        except Exception as e:
            print(f"❌ 测试废弃接口异常: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始用户行为追踪测试")
        print("=" * 50)
        
        # 登录
        if not await self.login():
            print("❌ 无法登录，跳过测试")
            return
        
        # 等待一秒让登录生效
        await asyncio.sleep(1)
        
        # 测试各种行为
        await self.test_article_view()
        await asyncio.sleep(0.5)
        
        await self.test_video_view()
        await asyncio.sleep(0.5)
        
        await self.test_like_interaction()
        await asyncio.sleep(0.5)
        
        await self.test_favorite_interaction()
        await asyncio.sleep(0.5)
        
        await self.test_search_behavior()
        await asyncio.sleep(0.5)
        
        # 检查记录
        await self.check_behavior_records()
        
        # 测试废弃接口
        await self.test_deprecated_apis()
        
        print("\n" + "=" * 50)
        print("🎉 测试完成！")
        print("\n📋 检查要点:")
        print("1. 中间件是否自动记录了所有行为")
        print("2. 数据库中是否有对应的记录")
        print("3. 废弃的接口是否仍然可用")
        print("4. 应用日志中是否有行为追踪的记录")
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    tester = BehaviorTrackingTester()
    try:
        await tester.run_all_tests()
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main())
